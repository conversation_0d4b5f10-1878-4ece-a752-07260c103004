#!/usr/bin/env python3
"""
Test script for the enhanced statistics collection
Tests the get_case_statistics function with sample data
"""

import sys
import os
sys.path.append('/app')

import pandas as pd
from Scheduler_Celery.tasks import get_case_statistics
from datetime import datetime

def test_statistics_collection():
    """Test the get_case_statistics function"""
    print("Testing statistics collection...")
    
    # Create sample DataFrame
    sample_cases = pd.DataFrame([
        {'id': 1, 'docket': '1:23-cv-00001', 'court': 'NYSD', 'title': 'Test Case 1'},
        {'id': 2, 'docket': '1:23-cv-00002', 'court': 'NYSD', 'title': 'Test Case 2'},
        {'id': 3, 'docket': '1:23-cv-00003', 'court': 'CACD', 'title': 'Test Case 3'},
    ])
    
    # Create sample tracking dictionary with enhanced statistics
    sample_tracking = {
        'cases_no_ip_then_some_ip': [('case1', 'trademark'), ('case2', 'patent')],
        'cases_no_ip_then_all_ip': [('case3', 'copyright')],
        'cases_some_ip_then_more_ip': [('case4', 'trademark'), ('case5', 'patent')],
        'cases_some_ip_then_all_ip': [('case6', 'trademark')],
        'cases_ip_regressed': [],
        'cases_already_all_ip': [('case7', 'trademark'), ('case8', 'patent'), ('case9', 'copyright')],
        'total_count': 9,
        'title_changes': [('case1', 'Old Title', 'New Title'), ('case2', 'Old Title 2', 'New Title 2')],
        'cases_with_more_steps': [('case1', 5, 8), ('case3', 3, 6), ('case4', 2, 5)],
        'cases_closed': [('case5', 'Closed')],
        'ip_sources_found': {
            'exhibit': [('case1', 'trademark', 'exhibit-12345'), ('case2', 'patent', 'att_1')],
            'byregno': [('case3', 'trademark', 'LLM/MainDoc/Unresolved'), ('case4', 'copyright', 'step_5')],
            'cn_website': [('case5', 'copyright', 'cn_website-10100-VA123456-image.jpg')],
            'bygoogle': [('case6', 'copyright', 'google-VA789012')],
            'manual': [('case7', 'trademark', 'manual_entry_001')]
        },
        'success_count': 8,
        'new_cases_count': 3,
        'cases_missing_ip_before': 6,
        'cases_missing_ip_after': 3,
        'fetch_type': 'weekly'
    }
    
    try:
        # Test the statistics collection
        stats = get_case_statistics(sample_cases, sample_tracking)
        
        print("Statistics collection successful!")
        print(f"Total cases processed: {stats['total_cases_processed']}")
        print(f"Fetch type: {stats['fetch_type']}")
        print(f"Success count: {stats['success_count']}")
        
        # Test IP statistics
        ip_stats = stats['ip_statistics']
        print(f"Cases no IP → some IP: {ip_stats['cases_no_ip_gained_some']}")
        print(f"Cases no IP → all IP: {ip_stats['cases_no_ip_gained_all']}")
        print(f"Cases some IP → more IP: {ip_stats['cases_some_ip_gained_more']}")
        print(f"Cases some IP → all IP: {ip_stats['cases_some_ip_gained_all']}")
        print(f"Cases IP regressed: {ip_stats['cases_ip_regressed']}")
        print(f"Cases already complete: {ip_stats['cases_already_complete']}")
        
        # Test enhanced statistics
        enhanced_stats = stats['enhanced_statistics']
        print(f"New cases count: {enhanced_stats['new_cases_count']}")
        print(f"Title changes count: {enhanced_stats['title_changes_count']}")
        print(f"Closed cases count: {enhanced_stats['closed_cases_count']}")
        print(f"Cases with more steps: {enhanced_stats['cases_with_more_steps']}")
        print(f"Cases missing IP before: {enhanced_stats['cases_missing_ip_before']}")
        print(f"Cases missing IP after: {enhanced_stats['cases_missing_ip_after']}")
        
        # Test IP sources
        ip_sources = enhanced_stats['ip_sources']
        print(f"IP from exhibits: {ip_sources['exhibit']}")
        print(f"IP from reg numbers: {ip_sources['byregno']}")
        print(f"IP from CN website: {ip_sources['cn_website']}")
        print(f"IP from Google: {ip_sources['bygoogle']}")
        print(f"IP manual entries: {ip_sources['manual']}")
        
        # Verify expected values
        assert stats['total_cases_processed'] == 3
        assert stats['fetch_type'] == 'weekly'
        assert ip_stats['cases_no_ip_gained_some'] == 2
        assert ip_stats['cases_no_ip_gained_all'] == 1
        assert enhanced_stats['title_changes_count'] == 2
        assert enhanced_stats['cases_with_more_steps'] == 3
        assert enhanced_stats['closed_cases_count'] == 1
        assert ip_sources['exhibit'] == 2
        assert ip_sources['byregno'] == 2
        assert ip_sources['cn_website'] == 1
        assert ip_sources['bygoogle'] == 1
        assert ip_sources['manual'] == 1
        
        print("All assertions passed!")
        return True
        
    except Exception as e:
        print(f"Error testing statistics collection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_statistics():
    """Test statistics collection with empty data"""
    print("Testing empty statistics collection...")

    empty_df = pd.DataFrame()
    empty_tracking = {
        'cases_no_ip_then_some_ip': 0,
        'cases_no_ip_then_all_ip': 0,
        'cases_some_ip_then_more_ip': 0,
        'cases_some_ip_then_all_ip': 0,
        'cases_ip_regressed': 0,
        'cases_already_all_ip': 0,
        'total_count': 0,
        'fetch_type': 'daily'
    }

    try:
        stats = get_case_statistics(empty_df, empty_tracking)

        print("Empty statistics collection successful!")
        print(f"No cases found: {stats['no_cases_found']}")
        print(f"Total cases processed: {stats['total_cases_processed']}")

        assert stats['no_cases_found'] == True
        assert stats['total_cases_processed'] == 0

        print("Empty statistics assertions passed!")
        return True

    except Exception as e:
        print(f"Error testing empty statistics: {e}")
        return False

def test_ip_source_tracking():
    """Test the enhanced IP source tracking functions"""
    print("Testing IP source tracking functions...")

    try:
        from Alerts.WorkflowManager import get_ip_sources, has_ip_data

        # Test IP state with various sources
        test_ip_state = {
            'trademark': {
                'is_relevant': True,
                'found_reg_nos': ['12345', '67890'],
                'processed_locations': ['exhibit-12345', 'att_1', 'google-67890']
            },
            'copyright': {
                'is_relevant': True,
                'found_reg_nos': ['VA123456'],
                'processed_locations': ['cn_website-10100-VA123456-image.jpg', 'LLM/MainDoc/Unresolved']
            },
            'patent': {
                'is_relevant': False,
                'found_reg_nos': [],
                'processed_locations': []
            }
        }

        # Test has_ip_data function
        has_ip = has_ip_data(test_ip_state)
        print(f"Has IP data: {has_ip}")
        assert has_ip == True

        # Test get_ip_sources function
        sources = get_ip_sources(test_ip_state)
        print(f"IP sources found: {sources}")

        # Should find exhibit, bygoogle, cn_website, and byregno sources
        expected_sources = ['exhibit', 'bygoogle', 'cn_website', 'byregno']
        for expected_source in expected_sources:
            assert expected_source in sources, f"Expected source '{expected_source}' not found in {sources}"

        # Test empty IP state
        empty_ip_state = {}
        assert has_ip_data(empty_ip_state) == False
        assert get_ip_sources(empty_ip_state) == []

        print("IP source tracking tests passed!")
        return True

    except Exception as e:
        print(f"Error testing IP source tracking: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all statistics tests"""
    print("=" * 50)
    print("Testing Enhanced Statistics Collection")
    print("=" * 50)
    
    tests = [
        ("Statistics Collection", test_statistics_collection),
        ("Empty Statistics", test_empty_statistics),
        ("IP Source Tracking", test_ip_source_tracking)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
        print(f"Result: {'PASS' if result else 'FAIL'}")
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print("=" * 50)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
