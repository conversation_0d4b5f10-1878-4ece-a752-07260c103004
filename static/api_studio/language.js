// Language support and translations
const i18n = {
    en: {
        home: 'Home',
        get_code: 'Get Code',
        input_form: 'Input Form',
        analysis_report: 'Analysis Report',
        api_key: 'API Key:',
        main_product_image_url: 'Main Product Image URL:',
        upload_main_image: 'Or Upload Main Product Image:',
        other_product_images: 'Other Product Images (Comma separated URLs):',
        upload_other_images: 'Or Upload Other Product Images:',
        ip_images: 'IP Images (Comma separated URLs):',
        upload_ip_images: 'Or Upload IP Images:',
        ip_keywords: 'IP Keywords (comma separated):',
        description: 'Description:',
        reference_text: 'Reference Text:',
        reference_images: 'Reference Images (comma separated URLs):',
        upload_reference_images: 'Or Upload Reference Images:',
        submit: 'Submit',
        clear_images: 'Clear selected images',
        check_id: 'Check ID:',
        risk_level: 'Risk Level:',
        risk_description: 'Risk Description:',
        type: 'Type:',
        ip_owner: 'IP Owner:',
        trademark_text: 'Trademark Text:',
        registration_number: 'Registration Number:',
        serial_number: 'Serial Number:',
        assignee: 'Assignee:',
        applicant: 'Applicant:',
        inventors: 'Inventors:',
        patent_title: 'Title:',
        show_report: 'Show Report',
        hide_report: 'Hide Report',
        product_image: 'Product Image:',
        ip_image: 'IP Image:',
        processing: 'Please wait while your request is being processed...',
        python_code_title: 'Python API Call Code',
        copy_code: 'Copy Code to Clipboard',
        code_copied: 'Code Copied!',
        number_of_cases: 'Number of Cases',
        last_case_docket: 'Last Case Docket',
        last_case_date: 'Last Case Date',
        no_report: 'No report available.',
        running: 'Running',
        estimated_time: 'Estimated time: ',
        error_rate_limit: 'Rate limit exceeded. Please try again later.',
        error_invalid_api_key: 'Invalid API Key. Please check and try again.',
        error_server: 'Server error occurred. Please try again later or contact support.',
        error_network: 'Network error. Please check your connection and try again.',
        error_timeout: 'Request timed out. The server is still processing your request.',
        error_missing_field: 'Missing required field',
        check_page: 'Check',
        reverse_check_page: 'Reverse Check',
        reverse_check_results: 'Reverse Check Results',
        start_date: 'Start Date:',
        end_date: 'End Date:',
        fetch_reverse_check: 'Fetch Reverse Check',
        available_reverse_checks: 'Available Reverse Checks',
        reverse_check_details: 'Reverse Check Details',
        select_reverse_check: 'Select a reverse check from the left panel to view details'
    },
    zh: {
        home: '首页',
        get_code: '获取代码',
        input_form: '输入表单',
        analysis_report: '分析报告',
        api_key: 'API密钥:',
        main_product_image_url: '主产品图片URL:',
        upload_main_image: '或上传主产品图片:',
        other_product_images: '其他产品图片(逗号分隔URL):',
        upload_other_images: '或上传其他产品图片:',
        ip_images: 'IP图片(逗号分隔URL):',
        upload_ip_images: '或上传IP图片:',
        ip_keywords: 'IP关键词(逗号分隔):',
        description: '产品描述:',
        reference_text: '参考文本:',
        reference_images: '参考图片(逗号分隔URL):',
        upload_reference_images: '或上传参考图片:',
        submit: '提交',
        clear_images: '清除已选图片',
        check_id: '检测ID:',
        risk_level: '风险等级:',
        risk_description: '风险描述:',
        type: '类型:',
        ip_owner: 'IP所有者:',
        trademark_text: '商标文本:',
        registration_number: '注册号:',
        serial_number: '序列号:',
        assignee: '受让人:',
        applicant: '申请人:',
        inventors: '发明人:',
        patent_title: '标题:',
        show_report: '显示报告',
        hide_report: '隐藏报告',
        product_image: '产品图片:',
        ip_image: 'IP图片:',
        processing: '请等待，您的请求正在处理中...',
        python_code_title: 'Python API调用代码',
        copy_code: '复制代码到剪贴板',
        code_copied: '代码已复制!',
        number_of_cases: '案件数量',
        last_case_docket: '最新案件编号',
        last_case_date: '最新案件日期',
        no_report: '无可用报告。',
        running: '运行中',
        estimated_time: '预计时间：',
        error_rate_limit: '超出访问频率限制，请稍后再试。',
        error_invalid_api_key: 'API密钥无效，请检查后重试。',
        error_server: '服务器错误，请稍后再试或联系支持。',
        error_network: '网络错误，请检查您的连接后重试。',
        error_timeout: '请求超时，服务器仍在处理您的请求。',
        error_missing_field: '缺少必填字段',
        check_page: '检测',
        reverse_check_page: '反向检测',
        reverse_check_results: '反向检测结果',
        start_date: '开始日期:',
        end_date: '结束日期:',
        fetch_reverse_check: '获取反向检测',
        available_reverse_checks: '可用反向检测',
        reverse_check_details: '反向检测详情',
        select_reverse_check: '从左侧面板选择一个反向检测以查看详情'
    }
};

// Initialize language on page load
function initializeLanguage() {
    const langSelect = document.getElementById('languageSelect');
    const currentLang = document.getElementById('language').value;
    langSelect.value = currentLang;
    langSelect.dispatchEvent(new Event('change'));
}

// Update language for all elements
function updateLanguage(lang) {
    // Update UI elements
    document.querySelectorAll('[data-i18n]').forEach(el => {
        const key = el.dataset.i18n;
        el.textContent = i18n[lang][key];
    });

    // Update existing reports and toggle buttons
    document.querySelectorAll('.report-text').forEach(reportDiv => {
        const isZh = lang === 'zh';
        const rawText = decodeURIComponent(isZh ? reportDiv.dataset.reportZh : reportDiv.dataset.reportEn);
        reportDiv.innerHTML = formatReportText(rawText);
    });

    // Update show/hide report button texts
    document.querySelectorAll('.show-report-button').forEach(button => {
        const reportId = button.dataset.reportId;
        const reportContainer = document.getElementById(reportId);
        if (reportContainer) {
            button.textContent = reportContainer.style.display === 'none' 
                ? i18n[lang].show_report 
                : i18n[lang].hide_report;
        }
    });
}

// Initialize language switcher
function initializeLanguageSwitcher() {
    const languageSelect = document.getElementById('languageSelect');
    if (languageSelect) {
        languageSelect.addEventListener('change', function() {
            const lang = this.value;
            document.getElementById('language').value = lang;
            updateLanguage(lang);
        });
    }
}

// Export functions for global access
window.i18n = i18n;
window.initializeLanguage = initializeLanguage;
window.updateLanguage = updateLanguage;
window.initializeLanguageSwitcher = initializeLanguageSwitcher;
