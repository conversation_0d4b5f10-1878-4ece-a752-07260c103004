import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import logging
import os
from langfuse import observe

logger = logging.getLogger(__name__)

@observe(name="Send Daily Report Email", capture_input=False, capture_output=False)
def send_daily_report_email(stats):
    try:
        # Determine report type and subject
        fetch_type = stats.get('fetch_type', 'daily')
        is_periodic = fetch_type != 'daily'

        # Create message
        msg = MIMEMultipart()
        msg['From'] = '<EMAIL>'
        # msg['To'] = '<EMAIL>, <EMAIL>'
        msg['To'] = '<EMAIL>'

        # Set subject based on fetch type
        if is_periodic:
            type_names = {
                'weekly': 'Weekly',
                'monthly_1': 'Monthly (Last Month)',
                'monthly_2': 'Monthly (2 Months Ago)',
                'monthly_3': 'Monthly (3 Months Ago)',
                'monthly_4': 'Monthly (Open Cases 3-18 Months Old)'
            }
            subject_prefix = type_names.get(fetch_type, 'Periodic')
            msg['Subject'] = f"{subject_prefix} Case Fetch Report - {stats['fetch_date']}"
        else:
            msg['Subject'] = f"Daily Case Fetch Report - {stats['fetch_date']}"
        
        # Check if no cases were found
        if stats.get('no_cases_found', False):
            report_title = f"{subject_prefix} Case Fetch Report 📊" if is_periodic else "Daily Case Fetch Report 📊"
            no_cases_message = "No Cases Found" if is_periodic else "No New Cases Found Today"

            body = f"""
                    {report_title}
                    {'=' * len(report_title)}

                    Date: {stats['fetch_date']}
                    Fetch Type: {fetch_type.replace('_', ' ').title()}

                    📭 {no_cases_message}

                    The system completed its scan but didn't find any cases in the specified date range. This could be due to:
                    • No new cases filed in the target period
                    • Weekend or holiday period
                    • Courts not in session
                    • All cases in the period already processed

                    The system will continue monitoring according to the schedule.

                    ---
                    Automated report from Case Management System
                """
        else:
            # Get statistics
            ip_stats = stats.get('ip_statistics', {})
            enhanced_stats = stats.get('enhanced_statistics', {})

            # Helper function to safely get integer values
            def safe_int(value):
                if isinstance(value, (list, tuple)):
                    return len(value) if value else 0
                return int(value) if value is not None else 0

            # Extract IP stats safely
            cases_gained_some = safe_int(ip_stats.get('cases_no_ip_gained_some', 0))
            cases_gained_all = safe_int(ip_stats.get('cases_no_ip_gained_all', 0))
            cases_improved = safe_int(ip_stats.get('cases_some_ip_gained_more', 0))
            cases_completed = safe_int(ip_stats.get('cases_some_ip_gained_all', 0))
            cases_regressed = safe_int(ip_stats.get('cases_ip_regressed', 0))
            cases_already_complete = safe_int(ip_stats.get('cases_already_complete', 0))
            total_processed = safe_int(ip_stats.get('total_processed', 0))

            # Calculate progress metrics
            cases_with_progress = cases_gained_some + cases_gained_all + cases_improved + cases_completed
            
            if is_periodic:
                # Enhanced periodic report
                report_title = f"{subject_prefix} Case Fetch Report 📊"
                body = f"""
                        {report_title}
                        {'=' * len(report_title)}

                        Date: {stats['fetch_date']}
                        Fetch Type: {fetch_type.replace('_', ' ').title()}
                        Total Cases Processed: {stats['total_cases_processed']}
                        Successfully Processed: {stats.get('success_count', 0)}

                        📊 Enhanced Statistics:
                        • New cases found: {enhanced_stats.get('new_cases_count', 0)}
                        • Cases with title changes: {enhanced_stats.get('title_changes_count', 0)}
                        • Cases that closed: {enhanced_stats.get('closed_cases_count', 0)}
                        • Cases with more steps: {enhanced_stats.get('cases_with_more_steps', 0)}

                        🔍 IP Tracking Details:
                        • Cases missing IP before processing: {enhanced_stats.get('cases_missing_ip_before', 0)}
                        • Cases missing IP after processing: {enhanced_stats.get('cases_missing_ip_after', 0)}
                        • Cases that gained IP: {enhanced_stats.get('cases_missing_ip_before', 0) - enhanced_stats.get('cases_missing_ip_after', 0)}

                        📍 IP Sources Found:
                        • From exhibits: {enhanced_stats.get('ip_sources', {}).get('exhibit', 0)}
                        • From registration numbers: {enhanced_stats.get('ip_sources', {}).get('byregno', 0)}
                        • From CN website: {enhanced_stats.get('ip_sources', {}).get('cn_website', 0)}
                        • From Google search: {enhanced_stats.get('ip_sources', {}).get('bygoogle', 0)}
                        • Manual entries: {enhanced_stats.get('ip_sources', {}).get('manual', 0)}

                        📈 Detailed IP Tracking:
                        • Cases with no IP → gained some IP: {cases_gained_some}
                        • Cases with no IP → gained all IP: {cases_gained_all}
                        • Cases with some IP → gained more IP: {cases_improved}
                        • Cases with some IP → gained all IP: {cases_completed}
                        • Cases where IP regressed: {cases_regressed}
                        • Cases already with complete IP: {cases_already_complete}

                        ---
                        Automated report from Case Management System
                    """
            else:
                # Standard daily report (unchanged format)
                body = f"""
                        Daily Case Fetch Report 📊
                        ==========================

                        Date: {stats['fetch_date']}

                        ✅ Successfully processed {stats['total_cases_processed']} new cases

                        🎯 Progress Summary:
                        • {cases_with_progress} cases made progress in IP collection
                        • {cases_already_complete} cases were already complete
                        • {total_processed} total cases processed

                        📈 Detailed Results:

                        New Cases That Gained IP:
                        • Started with no IP, now have some: {cases_gained_some}
                        • Started with no IP, now complete: {cases_gained_all}

                        Existing Cases That Improved:
                        • Had partial IP, gained more: {cases_improved}
                        • Had partial IP, now complete: {cases_completed}

                        Other Updates:
                        • Cases that lost IP: {cases_regressed}
                        • Cases already complete: {cases_already_complete}

                        🎉 Great job! The daily workflow completed successfully and all new cases have been processed through the IP analysis pipeline.

                        ---
                        Automated report from Case Management System
                    """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email via SMTP server
        with smtplib.SMTP('smtp.email.us-ashburn-1.oci.oraclecloud.com', 587) as server:
            server.starttls()
            server.login(os.getenv('OCI_EMAIL_USERNAME'), os.getenv('OCI_EMAIL_PASSWORD'))
            server.send_message(msg)

        
        logger.info("Daily report email sent successfully")
        
    except Exception as e:
        logger.error(f"Failed to send daily report email: {str(e)}")

@observe(name="Send Error Notification Email", capture_input=False, capture_output=False)
def send_error_notification_email(error_msg, task_name="Daily Case Fetch"):
    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = '<EMAIL>'
        msg['To'] = '<EMAIL>;<EMAIL>'
        msg['Subject'] = f"🚨 {task_name} - FAILED"
        
        # Email body
        body = f"""
                🚨 {task_name} Error Alert
                {'=' * (len(task_name) + 12)}

                The {task_name.lower()} task has failed with the following error:

                Error Details:
                --------------
                {error_msg}

                Action Required:
                ---------------
                Please check the application logs for more details and investigate the issue.

                Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

                ---
                Automated error notification from Case Management System
            """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email via SMTP server
        with smtplib.SMTP('smtp.email.us-ashburn-1.oci.oraclecloud.com', 587) as server:
            server.starttls()
            server.login(os.getenv('OCI_EMAIL_USERNAME'), os.getenv('OCI_EMAIL_PASSWORD'))
            server.send_message(msg)
        
        logger.info("Error notification email sent")
        
    except Exception as e:
        logger.error(f"Failed to send error notification email: {str(e)}")