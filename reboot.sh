#!/bin/bash

# --- Commands to run on boot ---

# 1. Restart cloudflared
# Use systemctl which is the modern way, fallback to service command
echo "Restarting cloudflared..."
service cloudflared restart
# Optional: Add a short pause if cloudflared takes time to stabilize
# sleep 5

# 2. Change to the workspace directory
# It's crucial to cd *before* sourcing the relative path ./set_env.sh
echo "Changing directory to /workspace..."
cd /workspace || { echo "Error: Could not change directory to /workspace" >&2; exit 1; }

# 3. Source the environment variables
echo "Sourcing /workspace/set_env.sh..."
source /workspace/set_env.sh || { echo "Error: Could not source /workspace/set_env.sh" >&2; exit 1; }

# 4. Run your Python application
echo "Starting Python application..."
# Use 'exec' so the python process replaces the script process, which is good for systemd.
# Use the full path to python if not in the PATH, or specify the virtual environment's python
exec /venv/main/bin/python /workspace/app_apistudio.py

# Note: Commands after 'exec' will only run if the python process fails to start
echo "Python application failed to start?" >&2
exit 1 # Exit with an error code if exec fails