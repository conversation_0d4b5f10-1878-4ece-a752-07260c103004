import sqlite3
from datetime import datetime
import threading
import os
from contextlib import contextmanager
import re # Keep re if used by OCR log update logic
import queue # Add this
from typing import Optional

# Thread-local storage
local = threading.local()

# if os.name == 'nt':
#     db_path = os.path.join(os.getcwd(), 'runs.db')
# else:
#     db_path = '/app/data/db/runs.db'

db_path = os.path.join(os.getcwd(), "data", 'runs.db')

@contextmanager
def task_context(run_id, step_name, progress_queue_for_case: Optional[queue.Queue] = None): # Add progress_queue_for_case
    set_context(run_id, step_name, progress_queue_for_case) # Pass it to set_context
    try:
        yield
    finally:
        clear_context()

def set_context(run_id, step_name, progress_queue_for_case: Optional[queue.Queue] = None): # Add progress_queue_for_case
    local.run_id = run_id
    local.step_name = step_name
    if progress_queue_for_case: # Store it if provided
        local.progress_queue = progress_queue_for_case
    else: # Ensure it's cleared if not provided or if task_context is used without it
        if hasattr(local, 'progress_queue'):
            del local.progress_queue


def clear_context():
    if hasattr(local, 'run_id'):
        del local.run_id
    if hasattr(local, 'step_name'):
        del local.step_name
    if hasattr(local, 'progress_queue'): # Clear the progress queue context too
        del local.progress_queue

def get_context():
    return getattr(local, 'run_id', None), getattr(local, 'step_name', None), getattr(local, 'progress_queue', None)

def log_message(message, level='INFO', exc_info=False): # Added exc_info for traceback
    run_id, step_name, progress_q = get_context() # Get progress_q
    if "text_in_page" not in message and "page" not in message and "done" not in message:
        print(f"{level}: {message}")
    if run_id is None or step_name is None:
        # print(f"{level}: {message}")  # Fallback logging
        return

    # Send to progress queue if available
    if progress_q:
        try:
            # Simple message format for the queue, can be enhanced
            queue_message = f"LOG [{level}]: {message}"
            if exc_info: # Add traceback to queue message if requested
                import traceback
                queue_message += f"\nTRACEBACK:\n{traceback.format_exc()}"
            progress_q.put_nowait(queue_message) # Use put_nowait to avoid blocking if queue is full
        except queue.Full:
            print(f"WARNING: Progress queue for case is full. Log message dropped: {message}")
        except Exception as e_q:
            print(f"ERROR: Failed to put log message into progress queue: {e_q}")

    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    timestamp = datetime.now().isoformat()
    try: # Add try-except for DB operations
        c.execute('SELECT id FROM steps WHERE run_id=? AND name=?', (run_id, step_name))
        step_id_row = c.fetchone()
        if not step_id_row:
            # This case should ideally be handled by ensuring run/step exists before logging
            print(f"WARNING: Step '{step_name}' not found for run_id '{run_id}'. DB log skipped for: {message}")
            return # Skip DB logging if step not found
        step_id = step_id_row[0]

        if "text_in_page" in message and "page" in message and "done" in message:

            # Extract filename from the message
            end_of_pdf = message.find(".pdf") + 4
            filename = message[:end_of_pdf]

            # Try to find an existing OCR log message for this step and filename
            c.execute('''
                SELECT id, message FROM logs 
                WHERE run_id=? AND step_id=? AND message LIKE ?
                ORDER BY id DESC LIMIT 1
            ''', (run_id, step_id, f'{filename}%'))
            existing_log = c.fetchone()

            if existing_log:
                # Update the existing log message
                existing_log_id, existing_log_message = existing_log  # Unpack both values
                new_page_number_match = re.search(r"page\s*(\d+)\s*done", existing_log_message)
                if new_page_number_match:
                    new_page_number = int(new_page_number_match.group(1)) + 1
                    message_to_db = re.sub(r"(page\s*)(\d+)(\s*done)", rf"\1{new_page_number}\3", message)
                    c.execute('UPDATE logs SET message=?, timestamp=? WHERE id=?',
                            (message_to_db, timestamp, existing_log_id))
            else:
                # Insert a new log message if no existing one is found
                message_to_db = re.sub(r"(page\s*)(\d+)(\s*done)", r"\1 1 \3", message)
                c.execute('INSERT INTO logs (run_id, step_id, timestamp, level, message) VALUES (?, ?, ?, ?, ?)',
                        (run_id, step_id, timestamp, level, message_to_db))
        else:
            # Insert a new log message for non-OCR messages
            c.execute('INSERT INTO logs (run_id, step_id, timestamp, level, message) VALUES (?, ?, ?, ?, ?)',
                    (run_id, step_id, timestamp, level, message))
    except sqlite3.Error as db_err:
        print(f"SQLite error in log_message: {db_err}") # Log DB errors
    finally:
        conn.commit()
        conn.close()


# Helper functions for database operations
def create_new_run():
    
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    start_time = datetime.now().isoformat()
    run_name = f"Batch Run - {start_time}" # Example name
    c.execute('INSERT INTO runs (name, start_time, status) VALUES (?, ?, ?)', (run_name, start_time, 'Running'))
    run_id = c.lastrowid
    steps = ['Get Cases', 'Process Pictures', 'Upload to Database', 'Upload Pictures', 'AI Tasks']
    for step in steps:
        c.execute('INSERT INTO steps (run_id, name, status) VALUES (?, ?, ?)', (run_id, step, 'Pending'))
    conn.commit()
    conn.close()
    print(f"Created new run id: {run_id} with name: {run_name}")
    return run_id

def create_reprocessing_run_and_step(case_id_as_run_id: int) -> tuple[int, str]:
    """
    Creates or replaces a run and a single step for a reprocessing task.
    The case_id is used as the run_id.
    Existing logs, steps, and run details for this run_id are deleted before creation.
    """
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    start_time = datetime.now().isoformat()
    try:
        # Delete existing data for this run_id
        c.execute('DELETE FROM logs WHERE run_id = ?', (case_id_as_run_id,))
        c.execute('DELETE FROM steps WHERE run_id = ?', (case_id_as_run_id,))
        # runs table will be handled by INSERT OR REPLACE

        run_name = f"Reprocessing Case {case_id_as_run_id}"
        initial_run_status = 'Pending' # Will be set to 'Running' by update_step_status

        # Use INSERT OR REPLACE for the runs table. Assumes 'name' column exists.
        c.execute('''
            INSERT OR REPLACE INTO runs (id, name, start_time, end_time, status) 
            VALUES (?, ?, ?, NULL, ?)
        ''', (case_id_as_run_id, run_name, start_time, initial_run_status))

        step_name = "Reprocessing" # Single step for the whole task
        # Clean up old step for this specific run_id and step_name before inserting a new one.
        c.execute('DELETE FROM steps WHERE run_id = ? AND name = ?', (case_id_as_run_id, step_name))
        c.execute('''
            INSERT INTO steps (run_id, name, status, start_time, end_time) 
            VALUES (?, ?, 'Pending', NULL, NULL)
        ''', (case_id_as_run_id, step_name))

        conn.commit()
        # Log this from queue_manager after task_context is set.
        print(f"DB: Created/Replaced run_id: {case_id_as_run_id} (maps to case_id) and step: '{step_name}' for reprocessing.")
    except sqlite3.Error as e:
        conn.rollback() # Rollback on error
        print(f"SQLite error in create_reprocessing_run_and_step: {e}")
        raise # Re-raise or handle appropriately
    finally:
        conn.close()
    return case_id_as_run_id, step_name


def update_step_status(run_id, step_name, status):
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        timestamp = datetime.now().isoformat()
        if status == 'Running':
            c.execute('UPDATE steps SET status=?, start_time=? WHERE run_id=? AND name=?', (status, timestamp, run_id, step_name))
        elif status in ['Completed', 'Failed', 'Warning']: # Ensure end_time is set for terminal states
            c.execute('UPDATE steps SET status=?, end_time=? WHERE run_id=? AND name=?', (status, timestamp, run_id, step_name))
        else: # For other statuses, just update status
            c.execute('UPDATE steps SET status=? WHERE run_id=? AND name=?', (status, run_id, step_name))
        conn.commit()
    except sqlite3.Error as e:
        print(f"SQLite error in update_step_status for run {run_id}, step {step_name}: {e}")
    finally:
        if conn:
            conn.close()


def finish_run(run_id, status):
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        end_time = datetime.now().isoformat()
        c.execute('UPDATE runs SET status=?, end_time=? WHERE id=?', (status, end_time, run_id))
        conn.commit()
    except sqlite3.Error as e:
        print(f"SQLite error in finish_run for run {run_id}: {e}")
    finally:
        if conn:
            conn.close()

def log_check_message(message, check_id, level='INFO'):
    timestamp = datetime.now().isoformat()

    conn = None
    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        c.execute('INSERT INTO check_logs (check_id, timestamp, level, message) VALUES (?, ?, ?, ?)',
                  (check_id, timestamp, level, message))
        conn.commit()
    except Exception as e:
        print(f"Error logging to database: {e}")
    finally:
        if conn:
            conn.close()


# Initialize the database
def init_db():
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()

        # Define expected schemas (column names are sufficient for this check)
        expected_schemas = {
            "runs": {"id", "name", "start_time", "end_time", "status"},
            "steps": {"id", "run_id", "name", "start_time", "end_time", "status"},
            "logs": {"id", "run_id", "step_id", "timestamp", "level", "message"},
            "check_logs": {"id", "check_id", "timestamp", "level", "message"}
        }

        # SQL for table creation
        create_statements = {
            "runs": '''CREATE TABLE runs (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            name TEXT,
                            start_time TEXT,
                            end_time TEXT,
                            status TEXT
                        )''',
            "steps": '''CREATE TABLE steps (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            run_id INTEGER,
                            name TEXT,
                            start_time TEXT,
                            end_time TEXT,
                            status TEXT,
                            FOREIGN KEY(run_id) REFERENCES runs(id)
                        )''',
            "logs": '''CREATE TABLE logs (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            run_id INTEGER,
                            step_id INTEGER,
                            timestamp TEXT,
                            level TEXT,
                            message TEXT,
                            FOREIGN KEY(run_id) REFERENCES runs(id),
                            FOREIGN KEY(step_id) REFERENCES steps(id)
                        )''',
            "check_logs": '''CREATE TABLE check_logs (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            check_id TEXT,
                            timestamp TEXT,
                            level TEXT,
                            message TEXT
                        )'''
        }

        for table_name, expected_cols in expected_schemas.items():
            recreate_table = False
            try:
                c.execute(f"PRAGMA table_info({table_name});")
                actual_cols_info = c.fetchall()
                if not actual_cols_info: # Table doesn't exist
                    recreate_table = True
                else:
                    actual_cols = {info[1] for info in actual_cols_info}
                    if not expected_cols.issubset(actual_cols): # Check if all expected columns are present
                        missing_cols = expected_cols - actual_cols
                        print(f"Schema mismatch for table '{table_name}'. Missing columns: {missing_cols}. Recreating.")
                        recreate_table = True
            except sqlite3.OperationalError as e:
                print(f"Operational error checking table '{table_name}': {e}. Assuming recreation is needed.")
                recreate_table = True

            if recreate_table:
                print(f"Recreating table '{table_name}'...")
                c.execute(f"DROP TABLE IF EXISTS {table_name};")
                c.execute(create_statements[table_name])
                print(f"Table '{table_name}' created successfully.")

        conn.commit()
    except sqlite3.Error as e:
        print(f"SQLite error during init_db: {e}")
    finally:
        if conn:
            conn.close()