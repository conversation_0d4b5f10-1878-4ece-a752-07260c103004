from Alerts.ReprocessCases import reprocess_cases
from Alerts.LexisNexis.Scrape_Date_Range import scrape_date_range
from DatabaseManagement.ImportExport import get_table_from_GZ
from datetime import date, timedelta
import asyncio
import pandas as pd
from logdata import log_message
from dateutil.relativedelta import relativedelta
import json


def get_month_range(months_ago):
    today = date.today()
    target_date = today - relativedelta(months=months_ago)
    start_date = target_date.replace(day=1)
    end_date = (start_date + relativedelta(months=1)) - timedelta(days=1)
    description = f"{start_date.strftime('%B %Y')}"
    return start_date, end_date, description

def get_date_range_for_period(period_type):
    today = date.today()

    if period_type == 'weekly':
        start_date = today - timedelta(days=7)
        end_date = today
        description = "last week"

    elif period_type == 'monthly_1':
        start_date, end_date, description = get_month_range(1)

    elif period_type == 'monthly_2':
        start_date, end_date, description = get_month_range(2)

    elif period_type == 'monthly_3':
        start_date, end_date, description = get_month_range(3)

    elif period_type == 'monthly_4':
        start_date = today - relativedelta(months=18)
        end_date = today - relativedelta(months=3)
        description = "open cases 3-18 months old"

    else:
        raise ValueError(f"Unknown period_type: {period_type}")

    return start_date, end_date, description

def capture_case_states(cases_to_process_df, full_cases_df):
    """
    Capture the current state of cases for before/after comparison
    """
    state_info = {}

    for idx, case_row in cases_to_process_df.iterrows():
        docket = case_row.get('docket')
        court = case_row.get('court')
        case_key = f"{docket}_{court}"

        # Find the case in the full database
        matching_cases = full_cases_df[
            (full_cases_df['docket'] == docket) &
            (full_cases_df['court'] == court)
        ]

        if not matching_cases.empty:
            db_case = matching_cases.iloc[0]

            # Extract current state information
            images_status = db_case.get('images_status', {})
            if isinstance(images_status, str):
                try:
                    images_status = json.loads(images_status)
                except:
                    images_status = {}

            # Get IP manager state if available
            ip_state = images_status.get('ip_manager_state', {}) if isinstance(images_status, dict) else {}

            state_info[case_key] = {
                'title': db_case.get('title', ''),
                'class_code': db_case.get('class_code', ''),
                'file_status': db_case.get('file_status', ''),
                'steps_processed': images_status.get('steps_processed', []) if isinstance(images_status, dict) else [],
                'number_of_pdfs': images_status.get('number_of_pdfs', 0) if isinstance(images_status, dict) else 0,
                'ip_state': ip_state,
                'is_new_case': False
            }
        else:
            # This is a new case
            state_info[case_key] = {
                'title': case_row.get('title', ''),
                'class_code': case_row.get('class_code', ''),
                'file_status': case_row.get('file_status', ''),
                'steps_processed': [],
                'number_of_pdfs': 0,
                'ip_state': {},
                'is_new_case': True
            }

    return state_info

def calculate_enhanced_statistics(before_state, after_state, new_cases_df, fetch_type):
    """
    Calculate enhanced statistics by comparing before and after states
    """
    stats = {
        'new_cases_count': len(new_cases_df),
        'title_changes_count': 0,
        'closed_cases_count': 0,
        'cases_with_more_steps': 0,
        'cases_missing_ip_before': 0,
        'cases_missing_ip_after': 0,
        'fetch_type': fetch_type,
        'ip_sources': {
            'exhibit': 0,
            'byregno': 0,
            'cn_website': 0,
            'bygoogle': 0,
            'manual': 0
        }
    }

    for case_key in before_state.keys():
        before = before_state[case_key]
        after = after_state.get(case_key, before)  # Use before state if after not found

        # Skip new cases for title/step comparisons
        if before['is_new_case']:
            continue

        # Check for title changes
        if before['title'] != after['title']:
            stats['title_changes_count'] += 1

        # Check for closure (class_code changes to closed or file_status indicates closure)
        before_closed = before['class_code'] in ['Closed', 'Terminated'] or 'closed' in before['file_status'].lower()
        after_closed = after['class_code'] in ['Closed', 'Terminated'] or 'closed' in after['file_status'].lower()
        if not before_closed and after_closed:
            stats['closed_cases_count'] += 1

        # Check for more steps
        before_steps = len(before['steps_processed'])
        after_steps = len(after['steps_processed'])
        if after_steps > before_steps:
            stats['cases_with_more_steps'] += 1

        # Check IP status changes
        before_has_ip = has_ip_data(before['ip_state'])
        after_has_ip = has_ip_data(after['ip_state'])

        if not before_has_ip:
            stats['cases_missing_ip_before'] += 1

        if not after_has_ip:
            stats['cases_missing_ip_after'] += 1

        # Track IP sources if IP was gained
        if not before_has_ip and after_has_ip:
            ip_sources = get_ip_sources(after['ip_state'])
            for source in ip_sources:
                if source in stats['ip_sources']:
                    stats['ip_sources'][source] += 1

    return stats

def has_ip_data(ip_state):
    """
    Check if the case has any IP data based on the IP manager state
    """
    if not isinstance(ip_state, dict):
        return False

    for ip_type in ['trademark', 'patent', 'copyright']:
        if ip_type in ip_state:
            type_state = ip_state[ip_type]
            if isinstance(type_state, dict):
                # Check if this IP type is relevant first
                if not type_state.get('is_relevant', False):
                    continue

                # Check found registration numbers
                found_reg_nos = type_state.get('found_reg_nos', set())
                if isinstance(found_reg_nos, (list, set)) and len(found_reg_nos) > 0:
                    return True

    return False

def get_ip_sources(ip_state):
    """
    Determine the sources where IP data was found based on processed_locations
    """
    sources = []
    if not isinstance(ip_state, dict):
        return sources

    for ip_type in ['trademark', 'patent', 'copyright']:
        if ip_type in ip_state:
            type_state = ip_state[ip_type]
            if isinstance(type_state, dict):
                # Check if IP was actually found
                found_reg_nos = type_state.get('found_reg_nos', [])
                if not found_reg_nos:
                    continue

                # Get processed locations to determine sources
                processed_locations = type_state.get('processed_locations', [])

                for location_id in processed_locations:
                    if isinstance(location_id, str):
                        # Categorize sources based on location_id patterns
                        if location_id.startswith('exhibit-') or location_id.startswith('att_'):
                            if 'exhibit' not in sources:
                                sources.append('exhibit')
                        elif location_id.startswith('google-'):
                            if 'bygoogle' not in sources:
                                sources.append('bygoogle')
                        elif location_id.startswith('cn_website-'):
                            if 'cn_website' not in sources:
                                sources.append('cn_website')
                        elif any(location_id.startswith(prefix) for prefix in ['byregno-', 'LLM/MainDoc', 'step_']):
                            if 'byregno' not in sources:
                                sources.append('byregno')
                        else:
                            # Default to manual for unknown sources
                            if 'manual' not in sources:
                                sources.append('manual')

    return sources

def run_case_fetch(period_type=None, nb_days=None):
    """
    Generalized fetch function to handle both periodic and daily fetches.
    If `period_type` is provided, uses predefined range logic.
    If `nb_days` is provided, fetches from today - nb_days to today.
    """

    today = date.today()
    if period_type:
        start_date, end_date, description = get_date_range_for_period(period_type)
    elif nb_days is not None:
        start_date = today - timedelta(days=nb_days)
        end_date = today
        description = f"last {nb_days} day(s)"
    else:
        raise ValueError("Either period_type or nb_days must be provided.")

    log_message(f"Starting fetch for {description} ({start_date} to {end_date})", level="INFO")

    # Load tables if using database filtering (skip if only daily and no database used)
    use_database = period_type is not None
    if use_database:
        log_message("Loading database tables...", level="INFO")
        all_cases_df = get_table_from_GZ("tb_case", force_refresh=True)
        plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)
    else:
        all_cases_df = None
        plaintiffs_df = None

    # Scrape cases (skip if monthly_4)
    if period_type == 'monthly_4':
        log_message("Skipping scraping for monthly_4 (database only)", level="INFO")
        new_cases_df = pd.DataFrame()
    else:
        log_message(f"Scraping cases from {start_date} to {end_date}...", level="INFO")
        new_cases_df = scrape_date_range(start_date, end_date, df_existing_cases=all_cases_df, plaintiff_df=plaintiffs_df)
        log_message(f"Found {len(new_cases_df)} new cases from scraping", level="INFO")

    # Filter open cases in DB (if applicable)
    if use_database and all_cases_df is not None and 'date_filed' in all_cases_df.columns:
        all_cases_df['date_filed'] = pd.to_datetime(all_cases_df['date_filed']).dt.date
        db_cases_df = all_cases_df[
            (all_cases_df['date_filed'] >= start_date) &
            (all_cases_df['date_filed'] <= end_date) &
            ((all_cases_df['class_code'] == 'Open') | (all_cases_df['class_code'].isna()))
        ].copy()
        log_message(f"Found {len(db_cases_df)} open cases from database", level="INFO")
    else:
        db_cases_df = pd.DataFrame()

    # Merge
    if not new_cases_df.empty and not db_cases_df.empty:
        combined_cases_df = pd.concat([new_cases_df, db_cases_df], ignore_index=True, sort=False)
        combined_cases_df = combined_cases_df.drop_duplicates(subset=['docket', 'court'], keep='first')
        log_message(f"Combined dataset has {len(combined_cases_df)} cases after de-duplication", level="INFO")
    elif not new_cases_df.empty:
        combined_cases_df = new_cases_df
        log_message("Using only scraped cases", level="INFO")
    elif not db_cases_df.empty:
        combined_cases_df = db_cases_df
        log_message("Using only DB-filtered cases", level="INFO")
    else:
        log_message("No cases to process", level="INFO")
        return pd.DataFrame(), {
            'cases_no_ip_then_some_ip': 0,
            'cases_no_ip_then_all_ip': 0,
            'cases_some_ip_then_more_ip': 0,
            'cases_some_ip_then_all_ip': 0,
            'cases_ip_regressed': 0,
            'cases_already_all_ip': 0,
            'total_count': 0,
            'new_cases_count': 0,
            'title_changes_count': 0,
            'closed_cases_count': 0,
            'cases_with_more_steps': 0,
            'cases_missing_ip_before': 0,
            'cases_missing_ip_after': 0,
            'fetch_type': period_type or 'daily'
        }

    # Capture before-processing state for enhanced tracking
    before_state = capture_case_states(combined_cases_df, all_cases_df if all_cases_df is not None else pd.DataFrame())

    processing_options = {
        'update_steps': True,
        'process_pictures': True,
        'upload_files_nas': True,
        'upload_files_cos': True,
        'run_plaintiff_overview': True,
        'run_summary_translation': True,
        'run_step_translation': True,
        'save_to_db': True,
        'processing_mode': 'resume' if period_type else 'full_reprocess',
        'refresh_days_threshold': 15
    }

    trace_label = f"{period_type.title() if period_type else 'Daily'} Fetch Workflow"
    all_cases_df = pd.concat([all_cases_df, new_cases_df], ignore_index=True, sort=False)
    success_status, tracking_dict = asyncio.run(
        reprocess_cases(
            cases_to_reprocess=combined_cases_df,
            processing_options=processing_options,
            trace_name=trace_label,
            full_cases_df=all_cases_df,
            plaintiff_df=plaintiffs_df
        )
    )

    # Capture after-processing state and calculate enhanced statistics
    after_state = capture_case_states(combined_cases_df, all_cases_df)
    enhanced_tracking = calculate_enhanced_statistics(before_state, after_state, new_cases_df, period_type or 'daily')

    # Merge the original tracking_dict with enhanced statistics
    tracking_dict.update(enhanced_tracking)

    return combined_cases_df, tracking_dict

# Convenience functions for each schedule type
def daily_fetch(nb_days=1):
    """Daily fetch - scrape cases from the last nb_days"""
    return run_case_fetch(nb_days=nb_days)

def weekly_fetch():
    """Saturday 1:30 AM NY - Search last week"""
    return run_case_fetch(period_type='weekly')

def monthly_fetch_1():
    """1st Sunday 1:30 AM NY - Search last month"""
    return run_case_fetch(period_type='monthly_1')

def monthly_fetch_2():
    """2nd Sunday 1:30 AM NY - Search 2 months ago"""
    return run_case_fetch(period_type='monthly_2')

def monthly_fetch_3():
    """3rd Sunday 1:30 AM NY - Search 3 months ago"""
    return run_case_fetch(period_type='monthly_3')

def monthly_fetch_4():
    """4th Sunday 1:30 AM NY - Search open cases 3-18 months old (database only, no scraping)"""
    return run_case_fetch(period_type='monthly_4')

if __name__ == '__main__':
    # Test different fetch types
    # fetch()  # Daily fetch
    weekly_fetch()  # Weekly fetch (last week)
    # monthly_fetch_1()  # Monthly fetch (last month)
    # monthly_fetch_2()  # Monthly fetch (2 months ago)
    # monthly_fetch_3()  # Monthly fetch (3 months ago)
    # monthly_fetch_4()  # 4th Sunday fetch (open cases 3-18 months old, database only)

    # # Test date range calculation
    # for period in ['weekly', 'monthly_1', 'monthly_2', 'monthly_3', 'monthly_4']:
    #     start, end, desc = get_date_range_for_period(period)
    #     print(f"{period}: {desc} ({start} to {end})")